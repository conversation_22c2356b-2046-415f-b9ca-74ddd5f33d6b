from langchain_groq import ChatGroq
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.prebuilt import create_react_agent
from loguru import logger
import os
from dotenv import load_dotenv
load_dotenv()
api_key = os.getenv("GROQ_API_KEY")
model = ChatGroq(
    model="meta-llama/llama-4-scout-17b-16e-instruct",
    max_tokens=512,
    api_key=api_key
)


from langchain_postgres.vectorstores import PGVector
import os
from dotenv import load_dotenv
load_dotenv()
from sqlalchemy import create_engine
from langchain_google_genai import GoogleGenerativeAIEmbeddings
import logging
from langchain.tools.retriever import create_retriever_tool
from google.genai.types import Tool, FunctionDeclaration

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)  # Set to DEBUG for more logs if needed

# Load Google Application Credentials
credential_path = "gen-lang-client-0077680037-9adbc1746e59.json"
if not os.path.exists(credential_path):
    raise FileNotFoundError(f"Credential file '{credential_path}' not found.")
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credential_path

# Initialize Embedding Model
def initialize_embeddings():
    try:
        embeddings = GoogleGenerativeAIEmbeddings(
            model="models/text-embedding-004",
        )
        return embeddings
    except Exception as e:
        logger.error(f"Failed to initialize Google Generative AI embeddings: {str(e)}")
        raise RuntimeError(f"Failed to initialize embeddings: {str(e)}")

# PostgreSQL DB Connection
def get_engine():
    username = os.getenv('db_user')
    password = os.getenv('db_password')
    hostname = os.getenv('db_host')
    port = os.getenv('db_port')
    database_name = os.getenv('db_name')

    return create_engine(f'postgresql+psycopg://{username}:{password}@{hostname}:{port}/{database_name}')

embedding = initialize_embeddings()

collection_name = "ct2"

connection = get_engine()

vector_store = PGVector(
    embeddings=embedding,
    collection_name=collection_name,
    connection=connection,
    use_jsonb=True,
)



def vector_search(query: str):
    try:
        results = vector_store.similarity_search(k=10, query=query)
        return results
    except Exception as e:
        logger.error(f"Failed to perform vector search: {str(e)}")
        raise RuntimeError(f"Failed to perform vector search: {str(e)}")


tools = [sum_numbers, multiply_numbers]

system_prompt = """You are Samantha, a helpful math assistant with a warm personality.
You can help with basic math operations by using your tools.
Always use the tools when asked to do math calculations.
Your output will be converted to audio so avoid using special characters or symbols.
Keep your responses friendly and conversational."""

memory = InMemorySaver()

agent = create_react_agent(
    model=model,
    tools=tools,
    prompt=system_prompt,
    checkpointer=memory,
)

agent_config = {"configurable": {"thread_id": "default_user"}}
